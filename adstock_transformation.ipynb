

import numpy as np
import pandas as pd

def get_transformation_functions():
    """
    Return a dictionary of transformation functions for feature engineering.
    Optimized to use vectorized operations.
    """
    def safe_power(x, exp):
        return np.power(np.maximum(x, 0), exp)
    
    def safe_log(x):
        return np.log1p(np.maximum(x, 0))
    
    return {
        "Log": safe_log,
        "Root4": lambda x: safe_power(x, 0.4),
        "Root5": lambda x: safe_power(x, 0.5),
        "Root6": lambda x: safe_power(x, 0.6),
        "Root7": lambda x: safe_power(x, 0.7),
        "Root8": lambda x: safe_power(x, 0.8),
        "Root9": lambda x: safe_power(x, 0.9)
    }

def apply_adstock(df, promo_col, id_col, adstock_rate):
    """
    Apply adstock transformation to a promotional channel with specified rate.
    Optimized to use groupby and transform for better performance.

    Args:
        df (pd.DataFrame): Input dataframe
        promo_col (str): Name of promotional column
        id_col (str): Column that identifies time series groups
        adstock_rate (int): Adstock rate (0-100)

    Returns:
        tuple: (Column name, Series with adstocked values)
    """
    rate = adstock_rate / 100
    
    # Define a function to apply adstock within each group
    def adstock_group(group):
        result = np.zeros(len(group))
        cumulative = 0
        for i, value in enumerate(group):
            cumulative = value + rate * cumulative
            result[i] = cumulative
        return result
    
    # Apply the function to each group using transform
    adstocked_series = df.groupby(id_col)[promo_col].transform(
        lambda group: adstock_group(group.values)
    )
    
    adstock_col = f"{promo_col}_Adstock_{adstock_rate}"
    return adstock_col, adstocked_series

def apply_transformation(series, transform_name, transform_func, base_col_name):
    """
    Apply transformation function to a series.
    Optimized for direct vectorized operations.

    Args:
        series (pd.Series): Data to transform
        transform_name (str): Name of transformation
        transform_func (callable): Transformation function
        base_col_name (str): Base column name

    Returns:
        tuple: (Column name, Transformed series)
    """
    transformed_col = f"{base_col_name}_{transform_name}"
    transformed_series = transform_func(series)
    return transformed_col, transformed_series

def generate_features(df, promotional_columns, id_col):
    """
    Generate features by applying adstock and transformations.
    Optimized to avoid DataFrame fragmentation by collecting all new columns
    and adding them at once using concat.
    
    Args:
        df (pd.DataFrame): Input dataframe
        promotional_columns (list): List of promotional column names
        id_col (str): Column that identifies time series groups
        
    Returns:
        pd.DataFrame: DataFrame with generated features
    """
    base_df = df.copy()
    transformations = get_transformation_functions()
    adstock_rates = range(10, 100, 10)
    
    # Dictionary to collect all new columns
    new_columns_dict = {}
    
    # Calculate all adstock features and transformations
    for promo_col in promotional_columns:
        for adstock_rate in adstock_rates:
            # Apply adstock
            adstock_col, adstocked_series = apply_adstock(df, promo_col, id_col, adstock_rate)
            new_columns_dict[adstock_col] = adstocked_series
            
            # Apply transformations to this adstocked column
            for transform_name, transform_func in transformations.items():
                transformed_col, transformed_series = apply_transformation(
                    adstocked_series, transform_name, transform_func, adstock_col
                )
                new_columns_dict[transformed_col] = transformed_series
    
    # Create a DataFrame from all the new columns
    new_columns_df = pd.DataFrame(new_columns_dict, index=df.index)
    
    # Concatenate with the original DataFrame
    global_df = pd.concat([base_df, new_columns_df], axis=1)
    
    return global_df





# Example usage:
df=pd.read_excel('dummy1.xlsx')
promotional_columns=['PDE','Copay']
id_col='ID'
global_df3 = generate_features(df, promotional_columns, id_col)